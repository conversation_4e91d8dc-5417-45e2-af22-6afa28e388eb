"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import {
  Menu,
  X,
  Home,
  Briefcase,
  MessageCircle,
  User,
  Bell,
  Hammer,
  Users,
  Settings,
  LogOut,
  Search,
  Plus,
  BarChart3,
  Calendar,
  FileText,
  DollarSign,
  Shield,
  CheckCircle,
  Clock
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, <PERSON>et<PERSON><PERSON>ger, SheetTitle } from "@/components/ui/sheet"
import { Logo } from "@/components/logo"
import { ProfileIconWithBadge } from "@/components/ui/verified-badge"
import { useUser } from "@/contexts/user-context"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface NavigationItem {
  icon: any
  label: string
  href: string
  count?: number
  badge?: string
  isActive?: boolean
  subItems?: NavigationItem[]
}

export function UnifiedNavigation() {
  const [isOpen, setIsOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const { user, logout, notifications } = useUser()
  const router = useRouter()
  const pathname = usePathname()

  const unreadNotifications = (notifications || []).filter(n => !n.read).length

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Customer Navigation Items
  const customerNavItems: NavigationItem[] = [
    { icon: Home, label: "Dashboard", href: "/dashboard" },
    {
      icon: Hammer,
      label: "Projects",
      href: "/projects",
      subItems: [
        { icon: Plus, label: "Create Project", href: "/project/create" },
        { icon: Briefcase, label: "My Projects", href: "/projects" },
        { icon: BarChart3, label: "Analytics", href: "/projects/analytics" }
      ]
    },
    { icon: Users, label: "Contractors", href: "/contractors" },
    {
      icon: MessageCircle,
      label: "Messages",
      href: "/messages",
      ...(unreadNotifications > 0 && { count: unreadNotifications })
    },
    {
      icon: Bell,
      label: "Notifications",
      href: "/notifications",
      ...(unreadNotifications > 0 && { count: unreadNotifications })
    },
  ]

  // Pro/Contractor Navigation Items
  const proNavItems: NavigationItem[] = [
    { icon: Home, label: "Dashboard", href: "/pro/dashboard" },
    {
      icon: Briefcase,
      label: "My Projects",
      href: "/pro/projects",
      subItems: [
        { icon: Clock, label: "Active", href: "/pro/projects?status=active" },
        { icon: FileText, label: "Bids", href: "/pro/bids" },
        { icon: CheckCircle, label: "Completed", href: "/pro/projects?status=completed" }
      ]
    },
    { icon: Calendar, label: "Schedule", href: "/pro/schedule" },
    {
      icon: MessageCircle,
      label: "Messages",
      href: "/messages",
      ...(unreadNotifications > 0 && { count: unreadNotifications })
    },
    { icon: BarChart3, label: "Analytics", href: "/pro/analytics" },
  ]

  const navigationItems = user?.role === 'pro' ? proNavItems : customerNavItems

  // Mark active items based on current path
  const itemsWithActiveState = navigationItems.map(item => ({
    ...item,
    isActive: pathname === item.href || pathname.startsWith(item.href + '/'),
    subItems: item.subItems?.map(subItem => ({
      ...subItem,
      isActive: pathname === subItem.href || pathname.startsWith(subItem.href + '/')
    }))
  }))

  const handleLogout = async () => {
    await logout()
    router.push('/')
    setIsOpen(false)
  }

  const NavItem = ({ item, isMobile = false }: { item: NavigationItem; isMobile?: boolean }) => {
    const baseClasses = isMobile
      ? "flex items-center space-x-3 px-3 py-2.5 text-slate-600 hover:text-slate-900 hover:bg-slate-100 active:bg-slate-200 rounded-xl transition-all duration-150 group min-h-[44px]"
      : "relative flex items-center space-x-2 text-slate-600 hover:text-slate-900 transition-all duration-200 text-sm font-medium py-2 px-3 rounded-lg hover:bg-slate-50 group whitespace-nowrap"

    const activeClasses = item.isActive
      ? isMobile
        ? "bg-blue-100 text-blue-700"
        : "text-blue-600 bg-blue-50"
      : ""

    return (
      <Link
        href={item.href}
        className={cn(baseClasses, activeClasses)}
        onClick={() => isMobile && setIsOpen(false)}
      >
        {isMobile && (
          <item.icon className={cn(
            "h-4 w-4 transition-all duration-150 flex-shrink-0",
            item.isActive
              ? "text-blue-600"
              : "text-slate-500 group-hover:text-slate-700"
          )} />
        )}
        <span className={`${isMobile ? "font-medium text-sm" : "font-medium"} truncate`}>{item.label}</span>
        {item.count && item.count > 0 && (
          <span className={cn(
            "ml-auto text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium transition-all duration-150 flex-shrink-0",
            item.isActive
              ? "bg-blue-600 text-white"
              : "bg-red-500 text-white"
          )}>
            {item.count > 9 ? '9+' : item.count}
          </span>
        )}
        {item.badge && (
          <span className="ml-auto bg-emerald-100 text-emerald-700 text-xs px-1.5 py-0.5 rounded-full font-medium flex-shrink-0">
            {item.badge}
          </span>
        )}
      </Link>
    )
  }

  return (
    <>
      {/* Desktop Navigation */}
      <nav id="navigation" className="hidden md:flex items-center justify-between px-6 lg:px-8 py-4 border-b-2 border-slate-200 bg-white shadow-lg sticky top-0 z-50" role="navigation" aria-label="Main navigation">
        <div className="flex items-center space-x-4">
          {/* Enhanced Logo Section - Always Visible */}
          <div className="flex items-center space-x-3 min-w-0 flex-shrink-0">
            <Logo size="md" showText={true} />
            {user?.role === 'pro' && (
              <div className="flex items-center space-x-1.5 px-2.5 py-1 bg-emerald-500 rounded-full text-white text-xs font-semibold">
                <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                <span className="text-xs font-semibold text-white">Pro</span>
              </div>
            )}
          </div>

          {/* Navigation Items - Responsive */}
          {user && (
            <div className="flex items-center space-x-4 overflow-x-auto scrollbar-hide">
              {itemsWithActiveState.map((item) => (
                <NavItem key={item.label} item={item} />
              ))}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-4">
          {user ? (
            <>
              {/* Quick Actions */}
              <div className="flex items-center space-x-3">
                {user.role === 'customer' && (
                  <Button size="sm" asChild className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg shadow-blue-500/25 rounded-xl font-semibold px-4 py-2.5 transition-all duration-200 hover:scale-105">
                    <Link href="/project/create">
                      <Plus className="h-4 w-4 mr-2" />
                      New Project
                    </Link>
                  </Button>
                )}

                {user.role === 'pro' && (
                  <Button size="sm" asChild className="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white shadow-lg shadow-emerald-500/25 rounded-xl font-semibold px-4 py-2.5 transition-all duration-200 hover:scale-105">
                    <Link href="/pro/browse">
                      <Search className="h-4 w-4 mr-2" />
                      Find Projects
                    </Link>
                  </Button>
                )}
              </div>

              {/* Notifications */}
              <Link href="/notifications" className="relative p-3 rounded-xl hover:bg-gradient-to-r hover:from-slate-50 hover:to-slate-100/50 transition-all duration-200 group">
                <Bell className="h-5 w-5 text-slate-600 group-hover:text-slate-700 transition-colors duration-200" />
                {unreadNotifications > 0 && (
                  <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-lg shadow-red-500/30 animate-pulse">
                    {unreadNotifications > 9 ? '9+' : unreadNotifications}
                  </span>
                )}
              </Link>

              {/* User Menu */}
              <div className="flex items-center space-x-4 pl-6 border-l border-slate-200/60">


                <Link
                  href="/profile"
                  className="text-slate-600 hover:text-slate-900 transition-all duration-200 px-4 py-2.5 rounded-xl hover:bg-gradient-to-r hover:from-slate-50 hover:to-slate-100/50 group"
                >
                  <ProfileIconWithBadge
                    user={{
                      name: user.name,
                      role: user.role,
                      isVerified: user.verified || false,
                      isOnline: true // You can implement real online status logic here
                    }}
                    className="group-hover:scale-105 transition-transform duration-200"
                  />
                </Link>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleLogout}
                  className="text-slate-500 hover:text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200 p-2.5"
                  title="Sign out"
                >
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
            </>
          ) : (
            <div className="flex items-center space-x-4">
              <Button variant="ghost" asChild className="rounded-xl hover:bg-slate-100 transition-all duration-200 font-medium text-slate-600 hover:text-slate-900">
                <Link href="/login">Sign in</Link>
              </Button>
              <Button asChild className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl shadow-lg shadow-blue-500/25 font-semibold px-6 py-2.5 transition-all duration-200 hover:scale-105">
                <Link href="/register">Get Started</Link>
              </Button>
            </div>
          )}
        </div>
      </nav>

      {/* Mobile-Native Navigation - Ultra-minimal design */}
      <nav className="md:hidden flex items-center justify-between px-4 pt-safe-top pb-3 bg-white/98 backdrop-blur-xl sticky top-0 z-50 border-b border-slate-100/80">
        {/* Mobile Logo Section - Standardized */}
        <div className="flex items-center space-x-2.5 min-w-0 flex-shrink-0">
          <Logo size="sm" variant="default" showText={true} />
          {user?.role === 'pro' && (
            <div className="flex items-center space-x-1 px-2 py-1 bg-emerald-500 rounded-lg">
              <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
              <span className="text-[10px] font-semibold text-white uppercase tracking-wider">Pro</span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-1">
          {user && (
            <>
              {/* Notifications - Mobile-Native */}
              <Link href="/notifications" className="relative p-2.5 rounded-full hover:bg-slate-100 active:bg-slate-200 transition-all duration-150 touch-target-enhanced">
                <Bell className="h-5 w-5 text-slate-700" />
                {unreadNotifications > 0 && (
                  <span className="absolute -top-0.5 -right-0.5 bg-red-500 text-white text-[9px] rounded-full h-4 w-4 flex items-center justify-center font-medium">
                    {unreadNotifications > 9 ? '9+' : unreadNotifications}
                  </span>
                )}
              </Link>

              {/* Mobile Menu - Native-style */}
              <Sheet open={isOpen} onOpenChange={setIsOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-2.5 rounded-full hover:bg-slate-100 active:bg-slate-200 transition-all duration-150 touch-target-enhanced">
                    <Menu className="h-5 w-5 text-slate-700" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-full max-w-sm bg-white overflow-y-auto safe-area-inset">
                  {/* Accessibility Title - Hidden but required */}
                  <SheetTitle className="sr-only">Navigation Menu</SheetTitle>

                  <div className="flex flex-col h-full">
                    {/* Header - Mobile-Native with standardized logo */}
                    <div className="flex items-center justify-between pb-4 mb-2">
                      <div className="flex items-center space-x-2.5">
                        <Logo size="sm" variant="default" showText={true} />
                        {user?.role === 'pro' && (
                          <div className="flex items-center space-x-1 px-2 py-1 bg-emerald-500 rounded-lg">
                            <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                            <span className="text-[10px] font-semibold text-white uppercase tracking-wider">Pro</span>
                          </div>
                        )}
                      </div>
                      <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)} className="p-2.5 rounded-full hover:bg-slate-100 active:bg-slate-200 transition-all duration-150 touch-target-enhanced">
                        <X className="h-5 w-5 text-slate-600" />
                      </Button>
                    </div>

                    {/* User Info - Mobile-Native */}
                    <div className="py-3 mb-2">
                      <div className="flex items-center space-x-3 p-3 rounded-xl bg-slate-50">
                        <div className="w-10 h-10 bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <User className="h-5 w-5 text-white" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="font-medium text-slate-900 text-sm truncate">{user.name}</p>
                          <div className="flex items-center space-x-1.5">
                            <p className="text-xs text-slate-500 capitalize">{user.role}</p>
                            {user?.role === 'pro' && user.verified && (
                              <CheckCircle className="h-3 w-3 text-emerald-600 flex-shrink-0" />
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Navigation Items - Mobile-Native */}
                    <div className="flex-1 py-2 space-y-1">
                      {itemsWithActiveState.map((item) => (
                        <div key={item.label}>
                          <NavItem item={item} isMobile />
                          {item.subItems && (
                            <div className="ml-6 mt-1 space-y-0.5">
                              {item.subItems.map((subItem) => (
                                <NavItem key={subItem.label} item={subItem} isMobile />
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* Footer Actions - Mobile-Native */}
                    <div className="pt-3 mt-2 border-t border-slate-100 space-y-1">
                      <Link
                        href="/profile"
                        className="flex items-center space-x-3 px-3 py-2.5 text-slate-600 hover:text-slate-900 hover:bg-slate-100 active:bg-slate-200 rounded-xl transition-all duration-150"
                        onClick={() => setIsOpen(false)}
                      >
                        <Settings className="h-4 w-4" />
                        <span className="font-medium text-sm">Settings</span>
                      </Link>

                      <button
                        onClick={handleLogout}
                        className="flex items-center space-x-3 px-3 py-2.5 text-red-600 hover:text-red-700 hover:bg-red-50 active:bg-red-100 rounded-xl transition-all duration-150 w-full"
                      >
                        <LogOut className="h-4 w-4" />
                        <span className="font-medium text-sm">Sign out</span>
                      </button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </>
          )}
          
          {!user && (
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" asChild className="btn-native-ghost text-sm px-4 py-2">
                <Link href="/login">Sign in</Link>
              </Button>
              <Button size="sm" asChild className="btn-native-primary text-sm px-4 py-2">
                <Link href="/register">Sign up</Link>
              </Button>
            </div>
          )}
        </div>
      </nav>
    </>
  )
}
