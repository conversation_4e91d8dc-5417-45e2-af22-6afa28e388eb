import Link from "next/link"

interface LogoProps {
  variant?: "default" | "light" | "dark" | "minimal"
  size?: "xs" | "sm" | "md" | "lg" | "xl"
  showText?: boolean
  className?: string
}

// Professional RenovHub Logo SVG - DriveTime-Inspired Bold Design
function LogoIcon({ size, variant }: { size: string; variant: string }) {
  return (
    <svg
      viewBox="0 0 48 48"
      className={size}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      role="img"
      aria-label="RenovHub"
    >
      {/* Gradient Definitions */}
      <defs>
        <linearGradient id="logoGradientPrimary" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#1e40af" />
          <stop offset="50%" stopColor="#2563eb" />
          <stop offset="100%" stopColor="#1d4ed8" />
        </linearGradient>
        <linearGradient id="logoGradientSecondary" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#0f172a" />
          <stop offset="100%" stopColor="#334155" />
        </linearGradient>
      </defs>

      {/* Modern Background Shape */}
      <rect
        x="4"
        y="4"
        width="40"
        height="40"
        rx="12"
        fill={variant === "light" ? "#ffffff" : variant === "dark" ? "#0f172a" : "url(#logoGradientPrimary)"}
        className="drop-shadow-xl"
        stroke={variant === "default" ? "none" : variant === "light" ? "#e2e8f0" : "#334155"}
        strokeWidth="2"
      />

      {/* Bold House Structure */}
      <path
        d="M14 32V20L24 14L34 20V32H29V24H19V32H14Z"
        fill={variant === "light" ? "#0f172a" : variant === "dark" ? "#ffffff" : "#ffffff"}
        strokeWidth="1"
        strokeLinejoin="round"
      />

      {/* Professional Tool Icon */}
      <path
        d="M30 18L32.5 15.5L35 18L32.5 20.5L30 18Z"
        fill={variant === "light" ? "#2563eb" : variant === "dark" ? "#60a5fa" : "#fbbf24"}
        strokeWidth="0.5"
      />
      <rect
        x="28.5"
        y="19"
        width="3"
        height="1.5"
        fill={variant === "light" ? "#2563eb" : variant === "dark" ? "#60a5fa" : "#fbbf24"}
        rx="0.5"
      />

      {/* Modern Window */}
      <rect
        x="20"
        y="18"
        width="8"
        height="4"
        fill={variant === "light" ? "#ffffff" : variant === "dark" ? "#0f172a" : "#1e40af"}
        rx="1"
        stroke={variant === "light" ? "#e2e8f0" : "none"}
        strokeWidth="1"
      />

      {/* Professional Door */}
      <rect
        x="21"
        y="26"
        width="6"
        height="6"
        fill={variant === "light" ? "#ffffff" : variant === "dark" ? "#0f172a" : "#1e40af"}
        rx="1"
        stroke={variant === "light" ? "#e2e8f0" : "none"}
        strokeWidth="1"
      />

      {/* Door Handle */}
      <circle
        cx="25.5"
        cy="29"
        r="0.8"
        fill={variant === "light" ? "#64748b" : variant === "dark" ? "#94a3b8" : "#fbbf24"}
      />
    </svg>
  )
}

export function Logo({ variant = "default", size = "md", showText = true, className = "" }: LogoProps) {
  const textColor = variant === "light" ? "text-white" :
                   variant === "dark" ? "text-slate-900" :
                   ""

  const sizeConfig = {
    xs: {
      text: "text-base sm:text-lg",
      icon: "h-7 w-7 flex-shrink-0",
      spacing: "space-x-2"
    },
    sm: {
      text: "text-lg sm:text-xl",
      icon: "h-8 w-8 flex-shrink-0",
      spacing: "space-x-2.5"
    },
    md: {
      text: "text-xl sm:text-2xl",
      icon: "h-9 w-9 flex-shrink-0",
      spacing: "space-x-2.5"
    },
    lg: {
      text: "text-2xl sm:text-3xl",
      icon: "h-10 w-10 flex-shrink-0",
      spacing: "space-x-3"
    },
    xl: {
      text: "text-3xl sm:text-4xl",
      icon: "h-12 w-12 flex-shrink-0",
      spacing: "space-x-3"
    }
  }

  const config = sizeConfig[size] || sizeConfig.md

  return (
    <Link
      href="/"
      className={`flex items-center ${config.spacing} font-medium ${textColor} hover:opacity-80 transition-opacity ${className}`}
    >
      <LogoIcon size={config.icon} variant={variant} />
      {showText && (
        <span className={`${config.text} tracking-tight font-bold ${
          variant === "light"
            ? "text-white"
            : variant === "dark"
            ? "text-slate-900"
            : "text-slate-800"
        } transition-colors duration-200`}
        style={{
          fontFamily: "'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
          fontWeight: 700,
          letterSpacing: '-0.02em',
          fontFeatureSettings: '"cv02", "cv03", "cv04", "cv11"'
        }}>
          RenovHub
        </span>
      )}
    </Link>
  )
}

// Logo variants for different use cases
export function LogoMark({ size = "md", variant = "default", className = "" }: Omit<LogoProps, "showText">) {
  return <Logo size={size} variant={variant} showText={false} className={className} />
}

export function LogoFull({ size = "md", variant = "default", className = "" }: Omit<LogoProps, "showText">) {
  return <Logo size={size} variant={variant} showText={true} className={className} />
}

// Simplified mobile logo - uses same desktop version
export function LogoMobile({ className = "", showText = true }: { className?: string; showText?: boolean }) {
  return <Logo size="sm" variant="default" showText={showText} className={className} />
}
