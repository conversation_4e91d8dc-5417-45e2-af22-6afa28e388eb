"use client"

import { useState, useRef, useEffect } from "react"
import { ArrowRight, Camera, Mic, MapPin, ChefHat, Bath, Hammer, Palette, DollarSign, Shield, Heart } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { RoleSwitcher } from "@/components/role-switcher"
import { Logo } from "@/components/logo"
import { useUser } from "@/contexts/user-context"
import { useRouter } from "next/navigation"
import { useToast } from "@/components/ui/toast-system"
import Link from "next/link"

export default function HomePage() {
  const [projectDescription, setProjectDescription] = useState("")
  const [isFocused, setIsFocused] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<File[]>([])
  const [location, setLocation] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const [isGettingLocation, setIsGettingLocation] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { user } = useUser()
  const router = useRouter()
  const { success, error, info } = useToast()

  // Auto-resize textarea based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [projectDescription])

  // Auto-focus the textarea when the page loads
  useEffect(() => {
    const timer = setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus()
      }
    }, 100) // Small delay to ensure the page is fully loaded

    return () => clearTimeout(timer)
  }, [])

  const handleStartProject = () => {
    if (projectDescription.trim()) {
      const params = new URLSearchParams({
        description: projectDescription,
        ...(location && { location }),
        ...(uploadedImages.length > 0 && { hasImages: 'true' })
      })
      router.push(`/project/create?${params.toString()}`)
    }
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length > 0) {
      const newImages = [...uploadedImages, ...files].slice(0, 5) // Limit to 5 images
      setUploadedImages(newImages)
      success(`${files.length} photo${files.length > 1 ? 's' : ''} added successfully`)

      if (files.length + uploadedImages.length > 5) {
        info("Maximum 5 photos allowed. Extra photos were not added.")
      }
    }
  }

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index))
  }

  const handleLocationRequest = () => {
    setIsGettingLocation(true)

    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            // Reverse geocoding to get address
            const response = await fetch(
              `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${position.coords.latitude}&longitude=${position.coords.longitude}&localityLanguage=en`
            )

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }

            const data = await response.json()

            // Check if we got valid location data
            if (data && (data.city || data.locality || data.principalSubdivision)) {
              const city = data.city || data.locality || 'Unknown City'
              const state = data.principalSubdivision || data.countryName || 'Unknown State'
              const locationString = `${city}, ${state}`
              setLocation(locationString)
              success(`Location set to ${locationString}`)
            } else {
              // Fallback to coordinates if no readable address
              const coords = `${position.coords.latitude.toFixed(4)}, ${position.coords.longitude.toFixed(4)}`
              setLocation(coords)
              info(`Location set to coordinates: ${coords}`)
            }
          } catch (error) {
            // Better error handling
            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            console.warn('Geocoding failed:', errorMessage)

            // Fallback to coordinates
            const coords = `${position.coords.latitude.toFixed(4)}, ${position.coords.longitude.toFixed(4)}`
            setLocation(coords)
            info(`Location set to coordinates: ${coords}`)
          } finally {
            setIsGettingLocation(false)
          }
        },
        (geolocationError) => {
          const errorMessages = {
            1: 'Location access denied. Please enable location services.',
            2: 'Location unavailable. Please check your connection.',
            3: 'Location request timed out. Please try again.'
          }

          const errorCode = geolocationError.code as keyof typeof errorMessages
          const errorMessage = errorMessages[errorCode] || 'Unable to get your location.'

          console.warn('Geolocation error:', errorMessage)
          setIsGettingLocation(false)
          error(errorMessage)

          // Fallback to manual input
          setTimeout(() => {
            const manualLocation = prompt('Please enter your location (City, State):')
            if (manualLocation && manualLocation.trim()) {
              setLocation(manualLocation.trim())
              success(`Location set to ${manualLocation.trim()}`)
            }
          }, 100)
        }
      )
    } else {
      setIsGettingLocation(false)
      error('Geolocation not supported in your browser.')

      setTimeout(() => {
        const manualLocation = prompt('Please enter your location (City, State):')
        if (manualLocation && manualLocation.trim()) {
          setLocation(manualLocation.trim())
          success(`Location set to ${manualLocation.trim()}`)
        }
      }, 100)
    }
  }

  const handleVoiceInput = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition
      const recognition = new SpeechRecognition()

      recognition.continuous = false
      recognition.interimResults = false
      recognition.lang = 'en-US'

      recognition.onstart = () => {
        setIsRecording(true)
      }

      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript
        setProjectDescription(prev => prev + (prev ? ' ' : '') + transcript)
        setIsRecording(false)
        success('Voice input added successfully')
      }

      recognition.onerror = () => {
        setIsRecording(false)
        error('Voice recognition failed. Please try again.')
      }

      recognition.onend = () => {
        setIsRecording(false)
      }

      recognition.start()
    } else {
      error('Speech recognition is not supported in your browser.')
    }
  }

  return (
    <div className="min-h-screen bg-white">
      <UnifiedNavigation />

      {/* Hero Section - Mobile-Native */}
      <div className="relative">
        <div className="container-native section-native-spacious">
          {/* Clean Hero - Mobile-Native */}
          <div className="text-center mb-6 sm:mb-8 lg:mb-12">
            <div className="space-y-5 sm:space-y-6">
              {/* Clean Hero Title - Mobile-First Typography */}
              <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-slate-900 leading-tight max-w-3xl mx-auto px-1">
                Renovate with{' '}
                <span className="bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">
                  Confidence
                </span>
              </h1>

              <p className="text-sm sm:text-base md:text-lg text-slate-600 max-w-2xl mx-auto leading-relaxed px-2">
                Get free quotes from verified contractors in minutes. Compare proposals and hire the perfect professional.
              </p>

              {/* Key Benefits - Responsive Layout with Icons */}
              <div className="pt-3 px-2">
                {/* Unified responsive layout to prevent hydration issues */}
                <div className="flex items-center justify-center gap-1 sm:gap-3 text-xs sm:text-sm">
                  <div className="flex items-center space-x-1 sm:space-x-2 bg-slate-50 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full">
                    <DollarSign className="w-3 h-3 sm:w-4 sm:h-4 text-green-600 flex-shrink-0" />
                    <span className="text-slate-700 font-medium">Free quotes</span>
                  </div>
                  <div className="flex items-center space-x-1 sm:space-x-2 bg-slate-50 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full">
                    <Shield className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600 flex-shrink-0" />
                    <span className="text-slate-700 font-medium">Verified pros</span>
                  </div>
                  <div className="flex items-center space-x-1 sm:space-x-2 bg-slate-50 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full">
                    <Heart className="w-3 h-3 sm:w-4 sm:h-4 text-purple-600 flex-shrink-0" />
                    <span className="text-slate-700 font-medium">No commitment</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Clean Project Input Section - Mobile-Native */}
          <div className="max-w-3xl mx-auto">
            <div className={`card-native-minimal mobile-spacing-md transition-all duration-200 ${isFocused ? "shadow-md border-blue-200" : ""}`}>
              <div className="text-center mb-4 sm:mb-6">
                <h2 className="text-native-title mb-2 sm:mb-3">
                  Describe your <span className="bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">project</span>
                </h2>
              </div>

              {/* Clean Textarea with Voice Input - Mobile-Native */}
              <div className="relative">
                <Textarea
                  ref={textareaRef}
                  placeholder="I want to transform my kitchen with modern cabinets, quartz countertops..."
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  className="min-h-[100px] sm:min-h-[120px] text-base border-slate-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500/20 rounded-xl resize-none pr-12"
                  maxLength={500}
                  aria-label="Describe your renovation project"
                  aria-describedby="project-description-help"
                />

                {/* Voice Input Button - Inside Textarea */}
                <button
                  onClick={handleVoiceInput}
                  disabled={isRecording}
                  className={`absolute top-3 right-3 p-2 rounded-lg transition-all duration-200 ${
                    isRecording
                      ? "bg-red-100 text-red-600 animate-pulse"
                      : "bg-slate-100 text-slate-600 hover:bg-slate-200 active:bg-slate-300"
                  }`}
                  aria-label={isRecording ? "Recording voice input" : "Start voice input"}
                >
                  <Mic className="h-4 w-4" />
                </button>

                <div className="absolute bottom-2 right-3 text-xs text-slate-400" aria-live="polite">
                  <span className={projectDescription.length > 450 ? 'text-amber-600 font-medium' : ''}>{projectDescription.length}</span>/500
                </div>
                <div id="project-description-help" className="sr-only">
                  Describe your renovation project in detail. Maximum 500 characters.
                </div>
              </div>

              {/* Enhanced Photo preview - Mobile-optimized */}
              {uploadedImages.length > 0 && (
                <div className="mt-6 p-4 bg-slate-50 rounded-xl border border-slate-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Camera className="h-4 w-4 text-slate-600" />
                      <span className="text-sm font-semibold text-slate-700">
                        {uploadedImages.length} photo{uploadedImages.length > 1 ? 's' : ''}
                      </span>
                    </div>
                    <span className="text-xs text-slate-500 bg-white px-2 py-1 rounded-md">
                      {5 - uploadedImages.length} remaining
                    </span>
                  </div>
                  <div className="grid grid-cols-3 sm:grid-cols-5 gap-3">
                    {uploadedImages.map((file, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Upload ${index + 1}`}
                          className="w-full aspect-square object-cover rounded-lg border border-slate-200 shadow-sm"
                        />
                        <button
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-sm font-medium opacity-100 sm:opacity-0 group-hover:opacity-100 transition-opacity shadow-lg touch-target-enhanced flex items-center justify-center"
                          aria-label={`Remove photo ${index + 1}`}
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Enhanced Location display - Mobile-optimized */}
              {location && (
                <div className="mt-6 p-4 bg-green-50 rounded-xl border border-green-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <MapPin className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-green-700">Location Set</p>
                        <p className="text-sm text-green-800 truncate">{location}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => setLocation("")}
                      className="text-green-600 hover:text-green-800 text-sm px-3 py-2 rounded-lg hover:bg-green-100 transition-colors font-medium touch-target-enhanced flex-shrink-0"
                      aria-label="Change location"
                    >
                      Change
                    </button>
                  </div>
                </div>
              )}

              {/* Clean action buttons - Separate Mobile/Desktop */}
              <div className="mt-4 pt-4 border-t border-slate-100">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                  className="hidden"
                />

                {/* Enhanced Mobile Layout: Better touch targets and spacing */}
                <div className="flex sm:hidden items-center gap-3">
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className={`flex items-center space-x-2 px-4 py-3 rounded-xl border text-sm font-medium transition-all flex-1 justify-center min-h-[48px] touch-target-enhanced ${
                      uploadedImages.length > 0
                        ? "bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100"
                        : "bg-slate-50 text-slate-600 border-slate-200 hover:bg-slate-100"
                    }`}
                    aria-label={`Add photos${uploadedImages.length > 0 ? ` (${uploadedImages.length})` : ''}`}
                  >
                    <Camera className="h-4 w-4 flex-shrink-0" />
                    <span>Photos</span>
                    {uploadedImages.length > 0 && <span className="text-xs">({uploadedImages.length})</span>}
                  </button>

                  <button
                    onClick={handleLocationRequest}
                    disabled={isGettingLocation}
                    className={`flex items-center space-x-2 px-4 py-3 rounded-xl border text-sm font-medium transition-all flex-1 justify-center min-h-[48px] touch-target-enhanced ${
                      location
                        ? "bg-green-50 text-green-700 border-green-200 hover:bg-green-100"
                        : "bg-slate-50 text-slate-600 border-slate-200 hover:bg-slate-100"
                    } ${isGettingLocation ? 'opacity-75' : ''}`}
                    aria-label={isGettingLocation ? 'Getting your location' : location ? 'Location set' : 'Add location'}
                  >
                    <MapPin className={`h-4 w-4 flex-shrink-0 ${isGettingLocation ? 'animate-spin' : ''}`} />
                    <span>{isGettingLocation ? 'Getting' : location ? 'Located' : 'Location'}</span>
                  </button>
                </div>

                {/* Desktop Layout: Original layout */}
                <div className="hidden sm:flex flex-col sm:flex-row items-stretch sm:items-center justify-between mobile-gap-sm">
                  <div className="flex flex-col sm:flex-row items-stretch sm:items-center mobile-gap-xs">
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className={`btn-native-secondary text-sm ${
                        uploadedImages.length > 0
                          ? "bg-blue-50 text-blue-700 border-blue-200"
                          : ""
                      }`}
                      aria-label={`Add photos to your project${uploadedImages.length > 0 ? ` (${uploadedImages.length} added)` : ''}`}
                    >
                      <Camera className="h-4 w-4 flex-shrink-0" aria-hidden="true" />
                      <span>
                        Photos {uploadedImages.length > 0 && `(${uploadedImages.length})`}
                      </span>
                    </button>



                    <button
                      onClick={handleLocationRequest}
                      disabled={isGettingLocation}
                      className={`flex items-center justify-center space-x-2 px-4 py-3 rounded-xl border transition-all duration-200 touch-target-enhanced text-sm font-medium ${
                        location
                          ? "border-green-200/60 bg-green-50 text-green-700"
                          : "border-slate-200/60 text-slate-600 hover:border-slate-300 hover:bg-slate-50"
                      }`}
                    >
                      <MapPin className={`h-4 w-4 flex-shrink-0 ${isGettingLocation ? 'animate-spin' : ''}`} />
                      <span>
                        {isGettingLocation ? 'Getting...' : location ? 'Located' : 'Location'}
                      </span>
                    </button>
                  </div>

                  {projectDescription && (
                    <button
                      onClick={handleStartProject}
                      className="btn-mobile-primary w-full sm:w-auto justify-center"
                      aria-label="Start your renovation project"
                    >
                      <span>Get Free Quotes</span>
                      <ArrowRight className="h-4 w-4 ml-2 flex-shrink-0" aria-hidden="true" />
                    </button>
                  )}
                </div>

                {/* Mobile Action Button */}
                {projectDescription && (
                  <div className="flex sm:hidden mt-3">
                    <button
                      onClick={handleStartProject}
                      className="btn-native-primary w-full justify-center"
                      aria-label="Start your renovation project"
                    >
                      <span>Get Free Quotes</span>
                      <ArrowRight className="h-4 w-4 ml-2 flex-shrink-0" aria-hidden="true" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Enhanced Get Inspired Section - Mobile-First */}
          {!projectDescription && (
            <div className="mt-12 sm:mt-16 lg:mt-20 -mx-4 sm:-mx-6 lg:-mx-8 xl:-mx-16 2xl:-mx-24">
              {/* Full Width Faded Background */}
              <div className="bg-gradient-to-br from-slate-50/80 via-blue-50/40 to-slate-50/80 py-8 sm:py-12 lg:py-16">
                <div className="container-mobile">
                  <div className="text-center mb-8 sm:mb-10 lg:mb-12">
                    <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-slate-900 mb-3 sm:mb-4 px-2">Get Inspired</h3>
                    <p className="text-base sm:text-lg text-slate-600 max-w-2xl mx-auto px-4">
                      Browse popular renovation projects or click any category to automatically fill your project description
                    </p>
                  </div>

              {/* Minimized Project Categories - Mobile-First Grid */}
              <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 max-w-5xl mx-auto mb-8 sm:mb-12 lg:mb-16">
                {[
                  {
                    name: "Kitchen Renovation",
                    icon: ChefHat,
                    description: "Modern kitchens with premium finishes",
                    color: "from-orange-500 to-red-500",
                    bgColor: "bg-orange-50",
                    borderColor: "border-orange-200/60"
                  },
                  {
                    name: "Bathroom Remodel",
                    icon: Bath,
                    description: "Spa-like bathrooms with luxury touches",
                    color: "from-blue-500 to-cyan-500",
                    bgColor: "bg-blue-50",
                    borderColor: "border-blue-200/60"
                  },
                  {
                    name: "Flooring Installation",
                    icon: Hammer,
                    description: "Hardwood, tile, and luxury vinyl",
                    color: "from-amber-500 to-yellow-500",
                    bgColor: "bg-amber-50",
                    borderColor: "border-amber-200/60"
                  },
                  {
                    name: "House Painting",
                    icon: Palette,
                    description: "Interior and exterior painting services",
                    color: "from-purple-500 to-pink-500",
                    bgColor: "bg-purple-50",
                    borderColor: "border-purple-200/60"
                  }
                ].map((example, index) => (
                  <button
                    key={index}
                    onClick={() => setProjectDescription(`I want to ${example.name.toLowerCase()} with ${example.description.toLowerCase()}`)}
                    className={`group p-3 sm:p-6 lg:p-8 ${example.bgColor} border ${example.borderColor} rounded-xl sm:rounded-2xl hover:shadow-xl hover:scale-[1.02] transition-all duration-300 text-center hover:bg-white touch-target-enhanced relative overflow-hidden`}
                  >
                    {/* Mobile: Compact layout */}
                    <div className="sm:hidden">
                      <div className={`w-8 h-8 bg-gradient-to-r ${example.color} rounded-lg flex items-center justify-center mx-auto mb-2 shadow-md group-hover:shadow-lg group-hover:scale-105 transition-all duration-300`}>
                        <example.icon className="h-4 w-4 text-white" />
                      </div>
                      <h4 className="text-sm font-bold text-slate-900 leading-tight">{example.name}</h4>
                    </div>

                    {/* Desktop: Enhanced layout */}
                    <div className="hidden sm:block">
                      <div className={`w-16 h-16 lg:w-20 lg:h-20 bg-gradient-to-br ${example.color} rounded-2xl flex items-center justify-center mx-auto mb-4 lg:mb-6 shadow-lg group-hover:shadow-2xl group-hover:scale-110 transition-all duration-500 relative`}>
                        <example.icon className="h-7 w-7 lg:h-8 lg:w-8 text-white" />
                        <div className="absolute inset-0 bg-white/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                      <h4 className="text-lg lg:text-xl font-bold text-slate-900 mb-3 group-hover:text-slate-700 transition-colors leading-tight">{example.name}</h4>
                      <p className="text-sm lg:text-base text-slate-600 leading-relaxed mb-4 group-hover:text-slate-500 transition-colors">{example.description}</p>
                      <div className="opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                        <span className="inline-flex items-center text-sm text-blue-600 font-semibold bg-blue-50 px-3 py-1.5 rounded-full">
                          Click to use
                          <ArrowRight className="h-3 w-3 ml-1" />
                        </span>
                      </div>
                    </div>

                    {/* Subtle background pattern for desktop */}
                    <div className="hidden sm:block absolute top-0 right-0 w-24 h-24 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
                      <example.icon className="w-full h-full text-slate-900" />
                    </div>
                  </button>
                ))}
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Enhanced How It Works Section */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 border border-slate-200/60 max-w-6xl mx-auto shadow-lg">
                <div className="text-center mb-6 sm:mb-8">
                  <h4 className="text-xl sm:text-2xl font-bold text-slate-900 mb-3 sm:mb-4">How RenovHub Works</h4>
                  <p className="text-sm sm:text-base text-slate-600 max-w-2xl mx-auto">
                    Get started with your renovation project in four simple steps
                  </p>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 relative">
                  {[
                    {
                      step: "1",
                      title: "Describe Your Project",
                      description: "Tell us about your renovation vision, budget, and timeline",
                      color: "bg-blue-600"
                    },
                    {
                      step: "2",
                      title: "Get Matched",
                      description: "We connect you with verified contractors in your area",
                      color: "bg-purple-600"
                    },
                    {
                      step: "3",
                      title: "Accept Offer",
                      description: "Review proposals and choose the best contractor for your project",
                      color: "bg-orange-600"
                    },
                    {
                      step: "4",
                      title: "Start Building",
                      description: "Manage your project and bring your dream to life",
                      color: "bg-green-600"
                    }
                  ].map((step, index) => (
                    <div key={index} className="text-center relative">
                      <div className={`w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 ${step.color} rounded-xl flex items-center justify-center text-white font-bold text-base sm:text-lg mx-auto mb-3 sm:mb-4 shadow-lg`}>
                        {step.step}
                      </div>
                      <h5 className="text-base sm:text-lg font-bold text-slate-900 mb-2 sm:mb-3">{step.title}</h5>
                      <p className="text-slate-600 leading-relaxed text-xs sm:text-sm">{step.description}</p>

                      {/* Stylish Faded Arrow */}
                      {index < 3 && (
                        <div className="hidden md:block absolute top-8 -right-3 transform translate-x-full">
                          <svg
                            className="w-6 h-6 text-slate-300"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M13 7l5 5m0 0l-5 5m5-5H6"
                              className="opacity-60"
                            />
                          </svg>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

          {/* Clean CTA Section */}
          <div className="text-center mt-16 mb-16">
            <div className="space-y-6">
              <h3 className="text-2xl lg:text-3xl font-bold text-slate-900">
                Ready to transform your space?
              </h3>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto leading-relaxed">
                Start your renovation journey today with trusted professionals in your area. Get free quotes with no commitment.
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Link href="/project/create">
                  <Button size="lg" className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-10 py-4 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 touch-target-enhanced">
                    Start Your Project Free
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/contractors">
                  <Button variant="outline" size="lg" className="bg-white/80 backdrop-blur-sm border-slate-200/60 hover:border-slate-300 hover:bg-white px-10 py-4 transition-all duration-200 hover:scale-105 touch-target-enhanced">
                    Browse Contractors
                  </Button>
                </Link>
              </div>
            </div>
          </div>

        </div>
      </div>

      <RoleSwitcher />
    </div>
  )
}
