@tailwind base;
@tailwind components;
@tailwind utilities;

/* DriveTime-Inspired Professional Design System */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Roboto:wght@300;400;500;600;700;800;900&display=swap');

/* Design Tokens */
:root {
  /* DriveTime-Inspired Professional Color Palette */
  --color-primary: 15 23 42; /* Professional dark slate */
  --color-primary-light: 51 65 85; /* Medium slate */
  --color-accent: 37 99 235; /* Professional blue */
  --color-accent-light: 96 165 250; /* Light blue */
  --color-success: 34 197 94; /* Professional green */
  --color-warning: 245 158 11; /* Professional amber */
  --color-error: 239 68 68; /* Professional red */

  /* Neutral Palette */
  --color-neutral-50: 248 250 252;
  --color-neutral-100: 241 245 249;
  --color-neutral-200: 226 232 240;
  --color-neutral-300: 203 213 225;
  --color-neutral-400: 148 163 184;
  --color-neutral-500: 100 116 139;
  --color-neutral-600: 71 85 105;
  --color-neutral-700: 51 65 85;
  --color-neutral-800: 30 41 59;
  --color-neutral-900: 15 23 42;

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Typography Scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

@layer base {
  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variant-ligatures: common-ligatures;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
    font-weight: 400;
    line-height: 1.5;
    color: rgb(var(--color-neutral-800));
    background-color: rgb(var(--color-neutral-50));
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  /* DriveTime-Inspired Professional Typography System */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
    font-weight: 700;
    line-height: 1.1;
    letter-spacing: -0.025em;
    color: rgb(var(--color-primary));
    margin-bottom: 0.5em;
  }

  h1 { font-size: var(--text-5xl); font-weight: 800; }
  h2 { font-size: var(--text-4xl); font-weight: 700; }
  h3 { font-size: var(--text-3xl); font-weight: 700; }
  h4 { font-size: var(--text-2xl); font-weight: 600; }
  h5 { font-size: var(--text-xl); font-weight: 600; }
  h6 { font-size: var(--text-lg); font-weight: 600; }

  p {
    line-height: 1.6;
    color: rgb(var(--color-neutral-600));
    font-size: var(--text-base);
    margin-bottom: 1em;
  }

  /* Professional text utilities */
  .text-professional {
    font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 500;
    letter-spacing: -0.01em;
  }

  .text-bold-professional {
    font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 700;
    letter-spacing: -0.02em;
  }

  /* Enhanced Focus States */
  *:focus-visible {
    outline: 2px solid rgb(var(--color-accent));
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
}

/* Premium Component System */
@layer components {
  /* Enhanced Layout Utilities */
  .container-premium {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-mobile-optimized {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    /* Enhanced mobile padding */
  }

  .section-premium {
    @apply py-16 lg:py-24;
  }

  .page-padding {
    @apply pt-8 pb-16 lg:pt-12 lg:pb-24;
  }

  .content-flow {
    @apply space-y-8 lg:space-y-12;
  }

  /* DriveTime-Inspired Professional Button System */
  .btn-premium {
    @apply inline-flex items-center justify-center font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
    letter-spacing: -0.01em;
  }

  .btn-primary {
    @apply btn-premium text-white focus-visible:ring-2 focus-visible:ring-offset-2;
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 50%, #1d4ed8 100%);
    padding: 0.875rem 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 14px rgba(30, 64, 175, 0.25);
    font-weight: 700;
    border: 2px solid transparent;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 50%, #1e40af 100%);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.35);
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .btn-secondary {
    @apply btn-premium bg-white text-slate-800 border-2 border-slate-300 hover:bg-slate-50 hover:border-slate-400 focus-visible:ring-slate-500;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    min-width: 120px;
    font-size: 0.875rem;
    font-weight: 600;
  }

  .btn-ghost {
    @apply btn-premium text-slate-700 hover:text-slate-900 hover:bg-slate-100 focus-visible:ring-slate-500;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
  }

  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: var(--text-sm);
  }

  .btn-lg {
    padding: 1rem 2rem;
    font-size: var(--text-lg);
  }

  /* Airbnb Card System */
  .card-premium {
    @apply bg-white border border-slate-200 transition-all duration-200;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  }

  .card-premium:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
    border-color: rgb(var(--color-neutral-300));
  }

  .card-interactive {
    @apply card-premium cursor-pointer;
  }

  .card-interactive:hover {
    transform: translateY(-2px);
  }

  /* DriveTime-Inspired Professional Input System */
  .input-premium {
    @apply w-full bg-white border-2 border-slate-300 text-slate-900 placeholder:text-slate-500 transition-all duration-200;
    padding: 1rem 1.25rem;
    border-radius: 12px;
    font-size: var(--text-base);
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .input-premium:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .textarea-premium {
    @apply input-premium resize-none;
    min-height: 140px;
  }
}

/* Premium Animations & Effects */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes gradient {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.5); }
}

/* Utility Classes */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animation-delay-1000 {
    animation-delay: 1s;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  /* Mobile-First Touch Targets */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .touch-target-large {
    @apply min-h-[48px] min-w-[48px];
  }

  /* Mobile-Optimized Spacing */
  .mobile-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .mobile-margin {
    @apply mx-4 sm:mx-6 lg:mx-8;
  }

  /* Standardized Action Button Positioning */
  .action-button-top-right {
    @apply absolute top-3 right-3 w-8 h-8 p-0 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .action-button-top-left {
    @apply absolute top-3 left-3 w-8 h-8 p-0 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md;
  }

  /* Responsive Text Scaling */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  /* Mobile-First Touch Targets */
  .touch-target-enhanced {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  .touch-target-large {
    @apply min-h-[48px] min-w-[48px] flex items-center justify-center;
  }

  /* Safe Area Support */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .pt-safe-top {
    padding-top: max(12px, env(safe-area-inset-top));
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Mobile-First Container */
  .container-mobile {
    @apply px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto;
  }

  .container-mobile-tight {
    @apply px-3 sm:px-4 lg:px-6 max-w-6xl mx-auto;
  }

  /* Mobile-First Spacing */
  .section-mobile {
    @apply py-6 sm:py-8 lg:py-12;
  }

  .section-mobile-tight {
    @apply py-4 sm:py-6 lg:py-8;
  }

  /* Mobile-First Cards */
  .card-mobile {
    @apply bg-white rounded-xl border border-slate-200/60 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .card-mobile-padding {
    @apply p-4 sm:p-6 lg:p-8;
  }

  /* Mobile-First Buttons */
  .btn-mobile-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl shadow-lg shadow-blue-500/25 font-semibold transition-all duration-200 hover:scale-105 touch-target-enhanced text-sm sm:text-base px-4 py-2.5 sm:px-6 sm:py-3;
  }

  .btn-mobile-secondary {
    @apply bg-white border border-slate-200 hover:border-slate-300 text-slate-700 hover:text-slate-900 rounded-xl shadow-sm hover:shadow-md font-medium transition-all duration-200 touch-target-enhanced text-sm sm:text-base px-4 py-2.5 sm:px-6 sm:py-3;
  }

  /* Enhanced Mobile-Native Form Elements */
  .input-mobile {
    @apply w-full px-4 py-3 border border-slate-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 text-base bg-white touch-target-enhanced;
  }

  .textarea-mobile {
    @apply w-full px-4 py-3 border border-slate-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 text-base bg-white resize-none min-h-[120px];
  }

  .input-mobile-enhanced {
    @apply w-full px-4 py-3.5 border border-slate-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 text-base bg-white touch-target-enhanced placeholder:text-slate-400 min-h-[48px];
  }

  .select-mobile {
    @apply w-full px-4 py-3.5 border border-slate-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 text-base bg-white touch-target-enhanced min-h-[48px] appearance-none;
  }

  /* Mobile-Native Spacing System */
  .mobile-spacing-xs { @apply p-2 sm:p-3; }
  .mobile-spacing-sm { @apply p-3 sm:p-4; }
  .mobile-spacing-md { @apply p-4 sm:p-6; }
  .mobile-spacing-lg { @apply p-6 sm:p-8; }
  .mobile-spacing-xl { @apply p-8 sm:p-12; }

  .mobile-gap-xs { @apply gap-2 sm:gap-3; }
  .mobile-gap-sm { @apply gap-3 sm:gap-4; }
  .mobile-gap-md { @apply gap-4 sm:gap-6; }
  .mobile-gap-lg { @apply gap-6 sm:gap-8; }

  .mobile-margin-xs { @apply m-2 sm:m-3; }
  .mobile-margin-sm { @apply m-3 sm:m-4; }
  .mobile-margin-md { @apply m-4 sm:m-6; }

  /* Mobile-Native Cards */
  .card-native {
    @apply bg-white rounded-2xl border-0 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .card-native-minimal {
    @apply bg-white rounded-xl border border-slate-100 shadow-none hover:shadow-sm transition-all duration-200;
  }

  .card-native-flat {
    @apply bg-slate-50/50 rounded-xl border-0 shadow-none;
  }

  /* Mobile-Native Containers */
  .container-native {
    @apply px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto;
  }

  .container-native-tight {
    @apply px-3 sm:px-4 lg:px-6 max-w-6xl mx-auto;
  }

  .container-native-full {
    @apply px-4 sm:px-6 w-full;
  }

  /* Mobile-Native Sections */
  .section-native {
    @apply py-4 sm:py-6 lg:py-8;
  }

  .section-native-tight {
    @apply py-3 sm:py-4 lg:py-6;
  }

  .section-native-spacious {
    @apply py-6 sm:py-8 lg:py-12;
  }

  /* Mobile-Native Typography - Optimized for small screens */
  .text-native-title {
    @apply text-base sm:text-lg lg:text-xl font-semibold text-slate-900 leading-tight;
  }

  .text-native-subtitle {
    @apply text-sm sm:text-base lg:text-lg text-slate-600 leading-relaxed;
  }

  .text-native-body {
    @apply text-sm sm:text-base text-slate-700 leading-relaxed;
  }

  .text-native-caption {
    @apply text-xs sm:text-sm text-slate-500;
  }

  .text-native-small {
    @apply text-xs text-slate-600;
  }

  /* Mobile-Native Buttons - Optimized sizes */
  .btn-native-primary {
    @apply bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white rounded-xl font-medium transition-all duration-150 touch-target-enhanced text-sm sm:text-base px-4 sm:px-6 py-2.5 sm:py-3 shadow-sm hover:shadow-md active:scale-95;
  }

  .btn-native-secondary {
    @apply bg-slate-100 hover:bg-slate-200 active:bg-slate-300 text-slate-700 rounded-xl font-medium transition-all duration-150 touch-target-enhanced text-sm sm:text-base px-4 sm:px-6 py-2.5 sm:py-3;
  }

  .btn-native-ghost {
    @apply bg-transparent hover:bg-slate-100 active:bg-slate-200 text-slate-600 rounded-xl font-medium transition-all duration-150 touch-target-enhanced text-sm sm:text-base px-3 sm:px-4 py-2;
  }

  .btn-native-compact {
    @apply bg-slate-100 hover:bg-slate-200 active:bg-slate-300 text-slate-700 rounded-lg font-medium transition-all duration-150 text-xs px-3 py-1.5;
  }

  /* Mobile-Native Lists */
  .list-native {
    @apply space-y-1 sm:space-y-2;
  }

  .list-item-native {
    @apply flex items-center p-3 sm:p-4 rounded-xl hover:bg-slate-50 active:bg-slate-100 transition-colors duration-150 touch-target-enhanced;
  }

  /* Mobile-Native Dividers */
  .divider-native {
    @apply border-t border-slate-100 my-4 sm:my-6;
  }

  .divider-native-subtle {
    @apply border-t border-slate-50 my-3 sm:my-4;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  /* Enhanced Mobile Interaction Patterns */
  .mobile-tap-highlight {
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  .mobile-scroll-smooth {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  .mobile-focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2;
  }

  /* Mobile-Optimized Grid Layouts */
  .grid-mobile-auto {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6;
  }

  .grid-mobile-cards {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
  }

  .grid-mobile-stats {
    @apply grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4;
  }

  /* Mobile-First Modal and Overlay Styles */
  .modal-mobile {
    @apply fixed inset-0 z-50 bg-white sm:bg-black/50 sm:flex sm:items-center sm:justify-center;
  }

  .modal-content-mobile {
    @apply w-full h-full sm:w-auto sm:h-auto sm:max-w-lg sm:max-h-[90vh] bg-white sm:rounded-2xl sm:shadow-2xl overflow-y-auto;
  }

  /* Enhanced Mobile Typography Scale */
  .text-mobile-xs { @apply text-xs leading-4; }
  .text-mobile-sm { @apply text-sm leading-5; }
  .text-mobile-base { @apply text-base leading-6; }
  .text-mobile-lg { @apply text-lg leading-7; }
  .text-mobile-xl { @apply text-xl leading-8; }

  /* Mobile-Optimized Spacing Utilities */
  .space-mobile-xs > * + * { @apply mt-2; }
  .space-mobile-sm > * + * { @apply mt-3; }
  .space-mobile-md > * + * { @apply mt-4; }
  .space-mobile-lg > * + * { @apply mt-6; }

  /* Premium Shadow System */
  .shadow-premium-sm {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04);
  }

  .shadow-premium-md {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.03);
  }

  .shadow-premium-lg {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.08), 0 4px 6px rgba(0, 0, 0, 0.04);
  }

  .shadow-premium-xl {
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 8px 10px rgba(0, 0, 0, 0.04);
  }

  .shadow-premium-2xl {
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12), 0 12px 20px rgba(0, 0, 0, 0.06);
  }

  .shadow-premium-3xl {
    box-shadow: 0 35px 60px rgba(0, 0, 0, 0.15), 0 15px 25px rgba(0, 0, 0, 0.08);
  }

  /* Colored Shadows */
  .shadow-blue {
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15), 0 4px 10px rgba(59, 130, 246, 0.08);
  }

  .shadow-emerald {
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.15), 0 4px 10px rgba(16, 185, 129, 0.08);
  }

  .shadow-purple {
    box-shadow: 0 10px 25px rgba(147, 51, 234, 0.15), 0 4px 10px rgba(147, 51, 234, 0.08);
  }

  /* Glass Morphism Effects */
  .glass-effect {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(12px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-effect-dark {
    background: rgba(15, 23, 42, 0.85);
    backdrop-filter: blur(12px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Premium Gradients */
  .gradient-premium-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }

  .gradient-premium-emerald {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
  }

  .gradient-premium-purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  }

  .gradient-premium-sunset {
    background: linear-gradient(135deg, #f59e0b 0%, #ef4444 50%, #ec4899 100%);
  }

  .gradient-premium-ocean {
    background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 50%, #8b5cf6 100%);
  }

  /* Accessibility Enhancements */
  .focus-visible-enhanced {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:ring-offset-white;
  }

  .focus-visible-enhanced-dark {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900;
  }

  /* High Contrast Mode Support */
  @media (prefers-contrast: high) {
    .text-slate-600 {
      @apply text-slate-800;
    }

    .text-slate-500 {
      @apply text-slate-700;
    }

    .border-slate-200 {
      @apply border-slate-400;
    }
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    .animate-spin,
    .animate-pulse,
    .animate-bounce,
    .animate-ping,
    .animate-fade-in,
    .animate-slide-up,
    .animate-gradient,
    .animate-float,
    .animate-glow {
      animation: none;
    }

    .transition-all,
    .transition-colors,
    .transition-transform,
    .transition-opacity {
      transition: none;
    }

    .hover\\:scale-105:hover,
    .hover\\:scale-110:hover,
    .group-hover\\:scale-110,
    .group-hover\\:scale-105 {
      transform: none;
    }
  }

  /* Screen Reader Only Content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }



  /* Enhanced Touch Targets for Mobile */
  .touch-target-enhanced {
    @apply min-h-[48px] min-w-[48px] flex items-center justify-center;
  }

  /* Color Blind Friendly Indicators */
  .status-indicator {
    position: relative;
  }

  .status-indicator::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  .status-success::before {
    background-color: #10b981;
  }

  .status-warning::before {
    background-color: #f59e0b;
  }

  .status-error::before {
    background-color: #ef4444;
  }

  .status-info::before {
    background-color: #3b82f6;
  }

  .loading-shimmer {
    background: linear-gradient(90deg,
      rgb(var(--color-neutral-100)) 25%,
      rgb(var(--color-neutral-200)) 50%,
      rgb(var(--color-neutral-100)) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  .text-balance {
    text-wrap: balance;
  }

  .transition-premium {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Status Colors */
  .status-active {
    @apply bg-blue-50 text-blue-700 border-blue-200;
  }

  .status-completed {
    @apply bg-emerald-50 text-emerald-700 border-emerald-200;
  }

  .status-pending {
    @apply bg-amber-50 text-amber-700 border-amber-200;
  }

  .status-draft {
    @apply bg-slate-50 text-slate-700 border-slate-200;
  }

  /* Custom Scrollbar */
  .scrollbar-premium::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-premium::-webkit-scrollbar-track {
    background: rgb(var(--color-neutral-100));
    border-radius: var(--radius-sm);
  }

  .scrollbar-premium::-webkit-scrollbar-thumb {
    background: rgb(var(--color-neutral-300));
    border-radius: var(--radius-sm);
  }

  .scrollbar-premium::-webkit-scrollbar-thumb:hover {
    background: rgb(var(--color-neutral-400));
  }

  /* Hide Scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Backdrop Effects */
  .backdrop-premium {
    backdrop-filter: blur(12px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.8);
  }

  /* Grid Background Pattern */
  .bg-grid-slate-100 {
    background-image:
      linear-gradient(to right, rgb(241 245 249) 1px, transparent 1px),
      linear-gradient(to bottom, rgb(241 245 249) 1px, transparent 1px);
    background-size: 24px 24px;
  }
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  /* RenovHub Professional Brand Colors - DriveTime Inspired */
  --brand-primary: oklch(0.35 0.18 230);        /* Deep Professional Blue */
  --brand-primary-foreground: oklch(0.98 0 0);
  --brand-secondary: oklch(0.25 0.05 240);      /* Professional Navy */
  --brand-secondary-foreground: oklch(0.98 0 0);
  --brand-accent: oklch(0.55 0.20 220);         /* Bright Professional Blue */
  --brand-accent-foreground: oklch(0.98 0 0);
  --brand-neutral: oklch(0.15 0.02 240);        /* Deep Charcoal */
  --brand-neutral-foreground: oklch(0.98 0 0);

  /* Status Colors */
  --status-success: oklch(0.55 0.15 140);       /* Success Green */
  --status-warning: oklch(0.70 0.20 70);        /* Warning Yellow */
  --status-error: oklch(0.55 0.20 25);          /* Error Red */
  --status-info: oklch(0.60 0.15 240);          /* Info Blue */

  /* Surface Colors */
  --surface-elevated: oklch(0.99 0 0);
  --surface-subtle: oklch(0.96 0 0);
  --surface-muted: oklch(0.94 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);

  /* RenovHub Brand Colors - Dark Mode */
  --brand-primary: oklch(0.60 0.15 220);        /* Lighter Blue for dark mode */
  --brand-primary-foreground: oklch(0.15 0 0);
  --brand-secondary: oklch(0.55 0.12 160);      /* Lighter Green for dark mode */
  --brand-secondary-foreground: oklch(0.15 0 0);
  --brand-accent: oklch(0.70 0.18 35);          /* Lighter Orange for dark mode */
  --brand-accent-foreground: oklch(0.15 0 0);
  --brand-neutral: oklch(0.85 0 0);             /* Light Gray for dark mode */
  --brand-neutral-foreground: oklch(0.15 0 0);

  /* Status Colors - Dark Mode */
  --status-success: oklch(0.65 0.15 140);
  --status-warning: oklch(0.75 0.20 70);
  --status-error: oklch(0.65 0.20 25);
  --status-info: oklch(0.70 0.15 240);

  /* Surface Colors - Dark Mode */
  --surface-elevated: oklch(0.20 0 0);
  --surface-subtle: oklch(0.18 0 0);
  --surface-muted: oklch(0.16 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* RenovHub Brand Colors */
  --color-brand-primary: var(--brand-primary);
  --color-brand-primary-foreground: var(--brand-primary-foreground);
  --color-brand-secondary: var(--brand-secondary);
  --color-brand-secondary-foreground: var(--brand-secondary-foreground);
  --color-brand-accent: var(--brand-accent);
  --color-brand-accent-foreground: var(--brand-accent-foreground);
  --color-brand-neutral: var(--brand-neutral);
  --color-brand-neutral-foreground: var(--brand-neutral-foreground);

  /* Status Colors */
  --color-status-success: var(--status-success);
  --color-status-warning: var(--status-warning);
  --color-status-error: var(--status-error);
  --color-status-info: var(--status-info);

  /* Surface Colors */
  --color-surface-elevated: var(--surface-elevated);
  --color-surface-subtle: var(--surface-subtle);
  --color-surface-muted: var(--surface-muted);

  /* Airbnb Brand Colors */
  --brand-primary: 255 90 95; /* Rausch */
  --brand-primary-foreground: 255 255 255;
  --brand-secondary: 0 132 137; /* Babu */
  --brand-secondary-foreground: 255 255 255;
  --brand-accent: 255 180 0; /* Arches */
  --brand-accent-foreground: 34 34 34;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }
}

@layer components {
  .text-balance {
    text-wrap: balance;
  }

  .animate-in {
    animation: animate-in 0.6s ease-out forwards;
  }

  .fade-in {
    animation: fade-in 0.4s ease-out forwards;
  }

  /* Font Utilities */
  .font-poppins {
    font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  .font-inter {
    font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  /* Brand Color Utilities */
  .bg-brand-primary {
    background-color: var(--brand-primary);
    color: var(--brand-primary-foreground);
  }

  .bg-brand-secondary {
    background-color: var(--brand-secondary);
    color: var(--brand-secondary-foreground);
  }

  .bg-brand-accent {
    background-color: var(--brand-accent);
    color: var(--brand-accent-foreground);
  }

  .text-brand-primary {
    color: var(--brand-primary);
  }

  .text-brand-secondary {
    color: var(--brand-secondary);
  }

  .text-brand-accent {
    color: var(--brand-accent);
  }

  .border-brand-primary {
    border-color: var(--brand-primary);
  }

  .border-brand-secondary {
    border-color: var(--brand-secondary);
  }

  .border-brand-accent {
    border-color: var(--brand-accent);
  }

  /* Status Color Utilities */
  .bg-status-success {
    background-color: var(--status-success);
    color: white;
  }

  .bg-status-warning {
    background-color: var(--status-warning);
    color: white;
  }

  .bg-status-error {
    background-color: var(--status-error);
    color: white;
  }

  .bg-status-info {
    background-color: var(--status-info);
    color: white;
  }

  .text-status-success {
    color: var(--status-success);
  }

  .text-status-warning {
    color: var(--status-warning);
  }

  .text-status-error {
    color: var(--status-error);
  }

  .text-status-info {
    color: var(--status-info);
  }
}

@keyframes animate-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Remove all unnecessary visual noise */
.minimal-input {
  @apply border-0 bg-transparent resize-none outline-none ring-0 focus:ring-0 focus:outline-none;
}

.minimal-button {
  @apply transition-all duration-200 ease-out;
}

.minimal-card {
  @apply bg-white border border-slate-100 transition-all duration-200;
}
